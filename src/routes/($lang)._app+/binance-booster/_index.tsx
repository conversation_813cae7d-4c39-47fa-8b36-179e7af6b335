import { useEffect, useState } from 'react';
import { AnimatePageLayout } from '@/components/ui/page';
import { Title } from '@/components/ui/title';
import { Text } from '@/components/ui/text';
import { cn } from '@/lib/utils';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import Autoplay from 'embla-carousel-autoplay';

// Carousel data for the 5 items
const carouselData = [
  {
    title: 'Connect Your Binance Wallet',
    subtitle: 'Link your Binance Web3 Wallet to get started',
    content:
      'Connect your Binance Web3 Wallet to participate in the BitVM and Binance Booster Campaign. This is the first step to unlock exclusive rewards and benefits.',
    image: '/images/ready-paly-one/dapps-list/binance.svg',
  },
  {
    title: 'Explore BitVM Technology',
    subtitle: 'Discover the power of Bitcoin Virtual Machine',
    content:
      "Learn about BitVM's revolutionary approach to Bitcoin scaling and smart contract functionality. Experience the future of Bitcoin development.",
    image: '/images/bitlayer-logo.png',
  },
  {
    title: 'Complete Campaign Tasks',
    subtitle: 'Participate in various activities and challenges',
    content:
      'Complete a series of tasks and challenges designed to showcase BitVM capabilities and earn rewards through the Binance Booster Campaign.',
    image: '/images/ready-paly-one/dapps-list/binance.svg',
  },
  {
    title: 'Earn Exclusive Rewards',
    subtitle: 'Get rewarded for your participation',
    content:
      'Earn exclusive rewards, tokens, and benefits by actively participating in the campaign. The more you engage, the more you earn.',
    image: '/images/bitlayer-logo.png',
  },
  {
    title: 'Join the Community',
    subtitle: 'Become part of the BitVM ecosystem',
    content:
      'Join our growing community of developers, users, and enthusiasts building the future of Bitcoin with BitVM technology.',
    image: '/images/ready-paly-one/dapps-list/binance.svg',
  },
];

// Loop Carousel Component
const LoopCarousel = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [selectedIndex, setSelectedIndex] = useState(0);

  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setSelectedIndex(api.selectedScrollSnap());
    };

    api.on('select', onSelect);
    onSelect();

    return () => {
      api.off('select', onSelect);
    };
  }, [api]);

  const plugins = [Autoplay({ delay: 3000, stopOnInteraction: false })];

  return (
    <div className="bl-w-full bl-max-w-4xl bl-mx-auto">
      <Carousel
        setApi={setApi}
        className="bl-w-full"
        opts={{
          loop: true,
          align: 'center',
        }}
        plugins={plugins}
      >
        <CarouselContent className="bl-ml-0">
          {carouselData.map((item, index) => (
            <CarouselItem key={index} className="bl-pl-4">
              <div
                className={cn(
                  'bl-bg-card-background bl-border bl-border-card-border bl-p-8 bl-rounded-lg',
                  'bl-transition-all bl-duration-500',
                  selectedIndex === index
                    ? 'bl-scale-100 bl-opacity-100 bl-border-primary'
                    : 'bl-scale-95 bl-opacity-70',
                )}
              >
                <div className="bl-flex bl-flex-col bl-items-center bl-text-center bl-space-y-6">
                  <div className="bl-w-16 bl-h-16 bl-flex bl-items-center bl-justify-center">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="bl-w-full bl-h-full bl-object-contain"
                    />
                  </div>

                  <div className="bl-space-y-3">
                    <Title variant="secondary" size="sm" className="bl-text-xl md:bl-text-2xl">
                      {item.title}
                    </Title>

                    <Text variant="default" size="md" className="bl-text-primary bl-font-medium">
                      {item.subtitle}
                    </Text>

                    <Text
                      variant="secondary"
                      size="sm"
                      className="bl-max-w-md bl-mx-auto bl-leading-relaxed"
                    >
                      {item.content}
                    </Text>
                  </div>
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>

      {/* Dots indicator */}
      <div className="bl-flex bl-justify-center bl-space-x-2 bl-mt-8">
        {carouselData.map((_, index) => (
          <button
            key={index}
            className={cn(
              'bl-w-3 bl-h-3 bl-rounded-full bl-transition-all bl-duration-300',
              selectedIndex === index
                ? 'bl-bg-primary bl-scale-125'
                : 'bl-bg-gray-600 hover:bl-bg-gray-500',
            )}
            onClick={() => api?.scrollTo(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default function BinanceBoosterPage() {
  return (
    <AnimatePageLayout>
      <div className="bl-min-h-screen bl-bg-background bl-text-foreground">
        {/* Header Section */}
        <section className="bl-container bl-pt-32 bl-pb-16 md:bl-pt-40 md:bl-pb-24">
          <div className="bl-flex bl-flex-col bl-items-center bl-text-center bl-space-y-8">
            {/* Logos */}
            <div className="bl-flex bl-items-center bl-justify-center bl-space-x-8 md:bl-space-x-12">
              <div className="bl-w-16 bl-h-16 md:bl-w-20 md:bl-h-20 bl-flex bl-items-center bl-justify-center">
                <img
                  src="/images/bitlayer-logo.png"
                  alt="Bitlayer Logo"
                  className="bl-w-full bl-h-full bl-object-contain"
                />
              </div>

              <div className="bl-w-1 bl-h-12 md:bl-h-16 bl-bg-primary bl-opacity-50"></div>

              <div className="bl-w-16 bl-h-16 md:bl-w-20 md:bl-h-20 bl-flex bl-items-center bl-justify-center">
                <img
                  src="/images/ready-paly-one/dapps-list/binance.svg"
                  alt="Binance Wallet Logo"
                  className="bl-w-full bl-h-full bl-object-contain"
                />
              </div>
            </div>

            {/* Title */}
            <Title
              variant="secondary"
              size="default"
              className="bl-text-3xl md:bl-text-5xl lg:bl-text-6xl bl-font-bold bl-leading-tight"
            >
              Bitlayer BitVM X Binance Booster Campaign
            </Title>

            {/* Subtitle */}
            <Text
              variant="default"
              size="lg"
              className="bl-text-lg md:bl-text-xl lg:bl-text-2xl bl-max-w-4xl bl-leading-relaxed"
            >
              Participate in the first week of the Booster Campaign now with Binance Wallet!
            </Text>
          </div>
        </section>

        {/* Main Carousel Section */}
        <section className="bl-container bl-py-16 md:bl-py-24">
          <div className="bl-flex bl-justify-center">
            <LoopCarousel />
          </div>
        </section>
      </div>
    </AnimatePageLayout>
  );
}

export { ErrorBoundary } from '@/components/featured/error-boundary';
