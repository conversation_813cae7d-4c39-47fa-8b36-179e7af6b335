import { useEffect, useState } from 'react';
import { AnimatePageLayout } from '@/components/ui/page';
import { Title } from '@/components/ui/title';
import { Text } from '@/components/ui/text';
import { cn } from '@/lib/utils';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import Autoplay from 'embla-carousel-autoplay';

// Carousel data for the 5 items
const carouselData = [
  {
    title: 'Connect Wallet',
    image: '/images/ready-paly-one/dapps-list/binance.svg',
  },
  {
    title: 'Explore BitVM',
    image: '/images/bitlayer-logo.png',
  },
  {
    title: 'Complete Tasks',
    image: '/images/ready-paly-one/dapps-list/binance.svg',
  },
  {
    title: 'Earn Rewards',
    image: '/images/bitlayer-logo.png',
  },
  {
    title: 'Join Community',
    image: '/images/ready-paly-one/dapps-list/binance.svg',
  },
];

// Loop Carousel Component
const LoopCarousel = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [selectedIndex, setSelectedIndex] = useState(0);

  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setSelectedIndex(api.selectedScrollSnap());
    };

    api.on('select', onSelect);
    onSelect();

    return () => {
      api.off('select', onSelect);
    };
  }, [api]);

  const plugins = [Autoplay({ delay: 3000, stopOnInteraction: false })];

  return (
    <div className="bl-w-full bl-flex bl-justify-center">
      <Carousel
        setApi={setApi}
        className="bl-w-full bl-max-w-7xl"
        opts={{
          loop: true,
          align: 'center',
          skipSnaps: false,
          containScroll: 'trimSnaps',
        }}
        plugins={plugins}
      >
        <CarouselContent className="bl-ml-0 bl-flex bl-items-center bl-justify-center bl-gap-4">
          {carouselData.map((item, index) => {
            const isActive = selectedIndex === index;
            return (
              <CarouselItem
                key={index}
                className={cn(
                  'bl-pl-4 bl-flex bl-justify-center',
                  'bl-basis-auto bl-flex-shrink-0',
                )}
              >
                <div
                  className={cn(
                    'bl-bg-card-background bl-border bl-border-card-border bl-rounded-lg',
                    'bl-transition-all bl-duration-500 bl-ease-in-out',
                    'bl-flex bl-flex-col bl-overflow-hidden',
                    isActive
                      ? 'bl-w-56 bl-h-[243px] bl-border-primary bl-shadow-lg'
                      : 'bl-w-[166px] bl-h-[180px] bl-border-gray-600',
                  )}
                >
                  {/* Title Section */}
                  <div
                    className={cn(
                      'bl-px-4 bl-py-3 bl-text-center bl-border-b bl-border-card-border',
                      isActive ? 'bl-py-4' : 'bl-py-2',
                    )}
                  >
                    <Title
                      variant="secondary"
                      size="xxs"
                      className={cn(
                        'bl-leading-tight bl-font-medium',
                        isActive ? 'bl-text-base' : 'bl-text-sm',
                      )}
                    >
                      {item.title}
                    </Title>
                  </div>

                  {/* Image Section */}
                  <div className="bl-flex-1 bl-flex bl-items-center bl-justify-center bl-p-4">
                    <div
                      className={cn(
                        'bl-flex bl-items-center bl-justify-center',
                        isActive ? 'bl-w-[120px] bl-h-[120px]' : 'bl-w-[90px] bl-h-[90px]',
                      )}
                    >
                      <img
                        src={item.image}
                        alt={item.title}
                        className="bl-w-full bl-h-full bl-object-contain"
                      />
                    </div>
                  </div>
                </div>
              </CarouselItem>
            );
          })}
        </CarouselContent>
      </Carousel>

      {/* Dots indicator */}
      <div className="bl-flex bl-justify-center bl-space-x-2 bl-mt-8">
        {carouselData.map((_, index) => (
          <button
            key={index}
            className={cn(
              'bl-w-3 bl-h-3 bl-rounded-full bl-transition-all bl-duration-300',
              selectedIndex === index
                ? 'bl-bg-primary bl-scale-125'
                : 'bl-bg-gray-600 hover:bl-bg-gray-500',
            )}
            onClick={() => api?.scrollTo(index)}
          />
        ))}
      </div>
    </div>
  );
};

export default function BinanceBoosterPage() {
  return (
    <AnimatePageLayout>
      <div className="bl-min-h-screen bl-bg-background bl-text-foreground">
        {/* Header Section */}
        <section className="bl-container bl-pt-32 bl-pb-16 md:bl-pt-40 md:bl-pb-24">
          <div className="bl-flex bl-flex-col bl-items-center bl-text-center bl-space-y-8">
            {/* Logos */}
            <div className="bl-flex bl-items-center bl-justify-center bl-space-x-8 md:bl-space-x-12">
              <div className="bl-w-16 bl-h-16 md:bl-w-20 md:bl-h-20 bl-flex bl-items-center bl-justify-center">
                <img
                  src="/images/bitlayer-logo.png"
                  alt="Bitlayer Logo"
                  className="bl-w-full bl-h-full bl-object-contain"
                />
              </div>

              <div className="bl-w-1 bl-h-12 md:bl-h-16 bl-bg-primary bl-opacity-50"></div>

              <div className="bl-w-16 bl-h-16 md:bl-w-20 md:bl-h-20 bl-flex bl-items-center bl-justify-center">
                <img
                  src="/images/ready-paly-one/dapps-list/binance.svg"
                  alt="Binance Wallet Logo"
                  className="bl-w-full bl-h-full bl-object-contain"
                />
              </div>
            </div>

            {/* Title */}
            <Title
              variant="secondary"
              size="default"
              className="bl-text-3xl md:bl-text-5xl lg:bl-text-6xl bl-font-bold bl-leading-tight"
            >
              Bitlayer BitVM X Binance Booster Campaign
            </Title>

            {/* Subtitle */}
            <Text
              variant="default"
              size="lg"
              className="bl-text-lg md:bl-text-xl lg:bl-text-2xl bl-max-w-4xl bl-leading-relaxed"
            >
              Participate in the first week of the Booster Campaign now with Binance Wallet!
            </Text>
          </div>
        </section>

        {/* Main Carousel Section */}
        <section className="bl-container bl-py-16 md:bl-py-24">
          <div className="bl-flex bl-justify-center">
            <LoopCarousel />
          </div>
        </section>
      </div>
    </AnimatePageLayout>
  );
}

export { ErrorBoundary } from '@/components/featured/error-boundary';
